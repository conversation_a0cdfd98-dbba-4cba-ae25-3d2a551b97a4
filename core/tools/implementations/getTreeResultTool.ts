import { ToolImpl } from ".";
import { getTreeResult } from "../../util/tree";

export const getTreeResultToolImpl: ToolImpl = async (args, extras) => {
  const targetDir = args?.targetDir;

  try {
    const result = await getTreeResult(extras.ide, targetDir);

    if (result.error) {
      return [
        {
          type: "error",
          name: "Tree Structure Error",
          description: "Error occurred while getting tree structure",
          content: `Failed to get tree structure: ${result.error}`,
        },
      ];
    }

    return [
      {
        type: "success",
        name: "Directory Tree Structure",
        description: targetDir
          ? `Tree structure of directory: ${targetDir}`
          : "Tree structure of workspace root directory",
        content: result.content,
      },
    ];
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    return [
      {
        type: "error",
        name: "Tree Structure Error",
        description: "Error occurred while getting tree structure",
        content: `Failed to get tree structure: ${errorMessage}`,
      },
    ];
  }
};
