import { ConfigDependentToolParams, IDE, Tool } from "..";
import { codebaseTool } from "./definitions/codebaseTool";
import { createNewFileTool } from "./definitions/createNewFile";
import { createRuleBlock } from "./definitions/createRuleBlock";
import { editFileTool } from "./definitions/editFile";
import { fetchUrlContentTool } from "./definitions/fetchUrlContent";
import { fileExistsTool } from "./definitions/fileExists";
import { getFileProblemsTool } from "./definitions/getFileProblems";
import { getProposedChangesTool } from "./definitions/getProposedChanges";
import { getTreeResultTool } from "./definitions/getTreeResult";
import { globSearchTool } from "./definitions/globSearch";
import { grepSearchTool } from "./definitions/grepSearch";
import { listDirTool } from "./definitions/listDir";
import { lsTool } from "./definitions/ls";
import { readCurrentlyOpenFileTool } from "./definitions/readCurrentlyOpenFile";
import { readFileTool } from "./definitions/readFile";
import { requestRuleTool } from "./definitions/requestRule";
import { runTerminalCommandTool } from "./definitions/runTerminalCommand";
import { searchAndReplaceInFileTool } from "./definitions/searchAndReplaceInFile";
import { searchWebTool } from "./definitions/searchWeb";
import { viewDiffTool } from "./definitions/viewDiff";
import { viewRepoMapTool } from "./definitions/viewRepoMap";
import { viewSubdirectoryTool } from "./definitions/viewSubdirectory";

// I'm writing these as functions because we've messed up 3 TIMES by pushing to const, causing duplicate tool definitions on subsequent config loads.

// missing support for remote os calls: https://github.com/microsoft/vscode/issues/252269
const getLocalOnlyToolDefinitions = () => [grepSearchTool];

const getBaseToolDefinitions = () => [
  codebaseTool,
  readFileTool,
  createNewFileTool,
  runTerminalCommandTool,
  globSearchTool,
  searchWebTool,
  viewDiffTool,
  readCurrentlyOpenFileTool,
  lsTool,
  getTreeResultTool,
  createRuleBlock,
  fetchUrlContentTool,
  fileExistsTool,
  getFileProblemsTool,
  getProposedChangesTool,
  listDirTool,
];

export const getConfigDependentToolDefinitions = (
  params: ConfigDependentToolParams,
): Tool[] => [
  requestRuleTool(params),
  // Search and replace is now generally available
  searchAndReplaceInFileTool,
  // Keep edit file tool available for models that need it
  editFileTool,
  ...(params.enableExperimentalTools
    ? [viewRepoMapTool, viewSubdirectoryTool]
    : []),
];

const getJetbrainsTools = () => {
  return [...getBaseToolDefinitions(), ...getLocalOnlyToolDefinitions()];
};

const getXcodeTools = () => {
  return [...getBaseToolDefinitions(), ...getLocalOnlyToolDefinitions()];
};

export const getToolsForIde = async (ide: IDE): Promise<Tool[]> => {
  if (await ide.isWorkspaceRemote()) {
    return getBaseToolDefinitions();
  } else {
    const ideInfo = await ide.getIdeInfo();
    switch (ideInfo.ideType) {
      case "jetbrains":
        return getJetbrainsTools();
      case "xcode":
        return getXcodeTools();
      default:
        return [...getBaseToolDefinitions(), ...getLocalOnlyToolDefinitions()];
    }
  }
};
